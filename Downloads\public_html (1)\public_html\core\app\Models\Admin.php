<?php

namespace App\Models;

use App\Constants\Status;
use App\Traits\GlobalStatus;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Schema;

class Admin extends Authenticatable
{
    use GlobalStatus;

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $fillable = [
        'name',
        'email',
        'username',
        'password',
        'image',
        'role_id',
        'is_super_admin',
        'status'
    ];

    protected $casts = [
        'is_super_admin' => 'boolean',
        'status' => 'boolean',
    ];

    protected $attributes = [
        'is_super_admin' => false,
        'status' => true,
    ];

    /**
     * Get the role that belongs to the admin.
     */
    public function role()
    {
        // Only return relationship if role_id column exists
        if (isset($this->attributes['role_id'])) {
            return $this->belongsTo(AdminRole::class, 'role_id');
        }
        return null;
    }

    /**
     * Check if admin is super admin
     */
    public function isSuperAdmin()
    {
        // Handle NULL values properly - if NULL, treat as false (not super admin)
        return (bool) ($this->is_super_admin ?? false);
    }

    /**
     * Check if admin has a specific permission
     */
    public function hasPermission($permission)
    {
        // Super admin has all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Check if admin has role and role has permission
        if ($this->role) {
            return $this->role->hasPermission($permission);
        }

        return false;
    }

    /**
     * Check if admin can access a specific route/action
     */
    public function canAccess($routeName)
    {
        // Super admin can access everything
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Map routes to permissions
        $routePermissions = $this->getRoutePermissions();

        foreach ($routePermissions as $pattern => $permission) {
            if (str_contains($routeName, $pattern)) {
                return $this->hasPermission($permission);
            }
        }

        return false;
    }

    /**
     * Get route to permission mappings
     */
    private function getRoutePermissions()
    {
        return [
            // Main
            'admin.dashboard' => 'dashboard',
            'admin.rides' => 'manage_rides',
            'admin.ride' => 'manage_rides',
            'admin.service' => 'system_setup',
            'admin.review' => 'all_reviews',
            'admin.promotional' => 'promotional_notify',

            // People
            'admin.rider' => 'manage_riders',
            'admin.driver' => 'manage_drivers',

            // Finance
            'admin.deposit' => 'driver_deposits',
            'admin.withdraw' => 'driver_withdrawals',
            'admin.gateway' => 'gateways',

            // Verification
            'admin.verification.driver' => 'driver_verification_form',
            'admin.verification.vehicle' => 'vehicle_verification_form',
            'admin.vehicle.verification' => 'vehicle_verification_form',

            // System Report
            'admin.report.rider' => 'rider_report',
            'admin.report.driver' => 'driver_report',
            'admin.report' => 'rider_report', // Default to rider report

            // System Utilities
            'admin.extensions' => 'manage_extension',
            'admin.seo' => 'manage_seo',
            'admin.language' => 'manage_language',

            // Admin Management
            'admin.staff' => 'manage_admins',
            'admin.roles' => 'manage_roles',

            // Settings
            'admin.setting.general' => 'general_settings',
            'admin.setting.brand' => 'brand_setting',
            'admin.setting.system.configuration' => 'system_configuration',
            'admin.setting.notification' => 'notification_setting',
            'admin.cron' => 'cron_job_setting',
            'admin.setting.cookie' => 'gdpr_cookie',
            'admin.setting.custom.css' => 'custom_css',
            'admin.setting.sitemap' => 'sitemap_xml',
            'admin.setting.robot' => 'robots_txt',
            'admin.maintenance.mode' => 'maintenance_mode',

            // Frontend Manager
            'admin.frontend.manage.pages' => 'manage_pages',
            'admin.frontend.sections' => 'manage_sections',
            'admin.frontend' => 'manage_pages', // Default to manage pages

            // Other
            'admin.ticket' => 'support_ticket',
            'admin.subscriber' => 'manage_subscriber',
            'admin.system' => 'application_information',
        ];
    }

    /**
     * Scope to get active admins
     */
    public function scopeActive($query)
    {
        return $query->where('status', Status::ENABLE);
    }

    /**
     * Scope to get non-super admins
     */
    public function scopeStaff($query)
    {
        try {
            // Check if is_super_admin column exists
            if (Schema::hasColumn('admins', 'is_super_admin')) {
                return $query->where(function($q) {
                    $q->where('is_super_admin', false)
                      ->orWhereNull('is_super_admin');
                });
            } else {
                // If column doesn't exist, return all admins except the first one (assumed to be super admin)
                return $query->where('id', '!=', 1);
            }
        } catch (\Exception $e) {
            // Fallback: return all admins except the first one
            return $query->where('id', '!=', 1);
        }
    }

    public function imageSrc(): Attribute
    {
        return new Attribute(
            get: fn() => $this->image  ? getImage(getFilePath('admin') . '/' . $this->image, getFileSize('admin')) : siteFavicon(),
        );
    }

    public function statusBadge(): Attribute
    {
        return new Attribute(
            get: fn() => ($this->status ?? true) ? '<span class="badge badge--success">Active</span>' : '<span class="badge badge--danger">Inactive</span>',
        );
    }

    public function roleBadge(): Attribute
    {
        return new Attribute(
            get: function() {
                if ($this->isSuperAdmin()) {
                    return '<span class="badge badge--warning">Super Admin</span>';
                }

                if ($this->role) {
                    return $this->role->roleBadge;
                }

                return '<span class="badge badge--secondary">No Role</span>';
            },
        );
    }
}
