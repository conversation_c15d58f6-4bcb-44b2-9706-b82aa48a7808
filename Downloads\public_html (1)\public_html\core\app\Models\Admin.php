<?php

namespace App\Models;

use App\Constants\Status;
use App\Traits\GlobalStatus;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Admin extends Authenticatable
{
    use GlobalStatus;

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $fillable = [
        'name',
        'email',
        'username',
        'password',
        'image',
        'role_id',
        'is_super_admin',
        'status'
    ];

    protected $casts = [
        'is_super_admin' => 'boolean',
        'status' => 'boolean',
    ];

    protected $attributes = [
        'is_super_admin' => false,
        'status' => true,
    ];

    /**
     * Get the role that belongs to the admin.
     */
    public function role()
    {
        // Only return relationship if role_id column exists
        if (isset($this->attributes['role_id'])) {
            return $this->belongsTo(AdminRole::class, 'role_id');
        }
        return null;
    }

    /**
     * Check if admin is super admin
     */
    public function isSuperAdmin()
    {
        // Handle NULL values properly - if NULL, treat as false (not super admin)
        return (bool) ($this->is_super_admin ?? false);
    }

    /**
     * Check if admin has a specific permission
     */
    public function hasPermission($permission)
    {
        // Super admin has all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Check if admin has role and role has permission
        if ($this->role) {
            return $this->role->hasPermission($permission);
        }

        return false;
    }

    /**
     * Check if admin can access a specific route/action
     */
    public function canAccess($routeName)
    {
        // Super admin can access everything
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Map routes to permissions
        $routePermissions = $this->getRoutePermissions();

        foreach ($routePermissions as $pattern => $permission) {
            if (str_contains($routeName, $pattern)) {
                return $this->hasPermission($permission);
            }
        }

        return false;
    }

    /**
     * Get route to permission mappings
     */
    private function getRoutePermissions()
    {
        return [
            // Driver Management
            'admin.driver' => 'driver_management',

            // Rider Management
            'admin.rider' => 'rider_management',

            // Ride Management
            'admin.rides' => 'ride_management',

            // Payment Management
            'admin.deposit' => 'payment_management',
            'admin.withdraw' => 'payment_management',
            'admin.gateway' => 'payment_management',

            // General Settings (more specific mappings)
            'admin.setting.general' => 'general_settings',
            'admin.setting.brand' => 'general_settings',
            'admin.setting.custom.css' => 'general_settings',
            'admin.setting.sitemap' => 'general_settings',
            'admin.setting.robot' => 'general_settings',
            'admin.setting.cookie' => 'general_settings',
            'admin.frontend' => 'general_settings',

            // Language Management
            'admin.language' => 'language_management',

            // SEO Settings
            'admin.seo' => 'seo_settings',

            // Reports & Analytics
            'admin.report' => 'reports_analytics',

            // System Configuration
            'admin.setting.system.configuration' => 'system_configuration',
            'admin.maintenance.mode' => 'system_configuration',
            'admin.cron' => 'system_configuration',
            'admin.system' => 'system_configuration',

            // Admin Management
            'admin.staff' => 'admin_management',
            'admin.roles' => 'admin_management',

            // Extensions
            'admin.extensions' => 'system_configuration',

            // Notification Settings
            'admin.setting.notification' => 'general_settings',

            // KYC/Verification
            'admin.verification' => 'driver_management',
            'admin.vehicle.verification' => 'vehicle_verification',

            // Subscriber Management
            'admin.subscriber' => 'general_settings',

            // Support Tickets
            'admin.ticket' => 'general_settings',
        ];
    }

    /**
     * Scope to get active admins
     */
    public function scopeActive($query)
    {
        return $query->where('status', Status::ENABLE);
    }

    /**
     * Scope to get non-super admins
     */
    public function scopeStaff($query)
    {
        return $query->where(function($q) {
            $q->where('is_super_admin', false)
              ->orWhereNull('is_super_admin');
        });
    }

    public function imageSrc(): Attribute
    {
        return new Attribute(
            get: fn() => $this->image  ? getImage(getFilePath('admin') . '/' . $this->image, getFileSize('admin')) : siteFavicon(),
        );
    }

    public function statusBadge(): Attribute
    {
        return new Attribute(
            get: fn() => ($this->status ?? true) ? '<span class="badge badge--success">Active</span>' : '<span class="badge badge--danger">Inactive</span>',
        );
    }

    public function roleBadge(): Attribute
    {
        return new Attribute(
            get: function() {
                if ($this->isSuperAdmin()) {
                    return '<span class="badge badge--warning">Super Admin</span>';
                }

                if ($this->role) {
                    return $this->role->roleBadge;
                }

                return '<span class="badge badge--secondary">No Role</span>';
            },
        );
    }
}
